# Hướng dẫn tái sử dụng logic Stop Order cho màn chỉnh sửa

## Tổng quan
Tài liệu này mô tả cách tái sử dụng logic và UI components từ màn đặt lệnh stop order cho màn chỉnh sửa lệnh stop order.

## Logic Đặt lệnh Stop Order hiện tại

### 1. UI Components chính:
- `StopOrderWidget` - Widget chính cho stop order
- `InputFieldBox` - Component input field tùy chỉnh với nút +/-
- `DerivativeValidateOrderCubit` - Quản lý validation và state

### 2. Cấu trúc logic:
```dart
class StopOrderWidget extends StatefulWidget {
  final TextEditingController priceController;
  // Logic validation qua DerivativeValidateOrderCubit
  // Focus management với FocusNode
  // Blink animation cho visual feedback
}
```

### 3. Validation & State Management:
- `DerivativeValidateOrderCubit` xử lý validation
- `FocusKeyboard` enum quản lý focus states
- Error handling qua `InputFieldError`

## Các cách tái sử dụng cho màn chỉnh sửa

### Cách 1: Tái sử dụng trực tiếp `InputFieldBox` component

**Ưu điểm:** Đơn giản, nhanh chóng
**Nhược điểm:** Code có thể bị duplicate

```dart
// Trong StopOrderEditDialog
InputFieldBox(
  controller: _volumeController,
  hintText: 'Khối lượng',
  onChange: (value) {
    final newVolume = int.tryParse(value) ?? 0;
    _updateVolume(newVolume);
  },
  focusNode: _volumeFocusNode,
  onTap: (increase) {
    _updateVolume(increase ? volume + 1 : volume - 1);
  },
  inputFormatters: [
    removeZeroStartInputFormatter,
    ...volumeInputFormatter,
  ],
),
```

### Cách 2: Tạo shared widget cho 3 trường input (Khuyến nghị)

**Ưu điểm:** Tái sử dụng cao, dễ maintain
**Nhược điểm:** Cần tạo thêm file

#### Tạo file shared component:
**File:** `features/vp_trading/lib/screen/place_order/derivative/shared/editable_stop_order_fields.dart`

```dart
class EditableStopOrderFields extends StatelessWidget {
  final TextEditingController volumeController;
  final TextEditingController priceController;
  final TextEditingController activePriceController;
  final Function(int) onVolumeChanged;
  final Function(double) onPriceChanged;
  final Function(double) onActivePriceChanged;

  const EditableStopOrderFields({
    super.key,
    required this.volumeController,
    required this.priceController,
    required this.activePriceController,
    required this.onVolumeChanged,
    required this.onPriceChanged,
    required this.onActivePriceChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildVolumeField(),
        const SizedBox(height: 16),
        _buildPriceField(),
        const SizedBox(height: 16),
        _buildActivePriceField(),
      ],
    );
  }

  Widget _buildVolumeField() {
    return Row(
      children: [
        Text('Khối lượng đặt', style: context.textStyle.body14),
        const SizedBox(width: 16),
        Expanded(
          child: InputFieldBox(
            controller: volumeController,
            hintText: 'KL',
            onChange: (value) {
              final newVolume = int.tryParse(value) ?? 0;
              onVolumeChanged(newVolume);
            },
            onTap: (increase) {
              final current = int.tryParse(volumeController.text) ?? 0;
              onVolumeChanged(increase ? current + 1 : current - 1);
            },
            inputFormatters: [
              removeZeroStartInputFormatter,
              ...volumeInputFormatter,
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPriceField() {
    return Row(
      children: [
        Text('Giá đặt', style: context.textStyle.body14),
        const SizedBox(width: 16),
        Expanded(
          child: InputFieldBox(
            controller: priceController,
            hintText: 'Giá',
            onChange: (value) {
              final newPrice = double.tryParse(value) ?? 0.0;
              onPriceChanged(newPrice);
            },
            onTap: (increase) {
              final current = double.tryParse(priceController.text) ?? 0.0;
              final step = 0.1; // Hoặc lấy từ stock info
              onPriceChanged(increase ? current + step : current - step);
            },
            inputFormatters: [
              removeZeroStartInputFormatter,
              ...priceInputFormatter,
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivePriceField() {
    return Row(
      children: [
        Text('Giá kích hoạt', style: context.textStyle.body14),
        const SizedBox(width: 16),
        Expanded(
          child: InputFieldBox(
            controller: activePriceController,
            hintText: 'Giá KH',
            onChange: (value) {
              final newActivePrice = double.tryParse(value) ?? 0.0;
              onActivePriceChanged(newActivePrice);
            },
            onTap: (increase) {
              final current = double.tryParse(activePriceController.text) ?? 0.0;
              final step = 0.1; // Hoặc lấy từ stock info
              onActivePriceChanged(increase ? current + step : current - step);
            },
            inputFormatters: [
              removeZeroStartInputFormatter,
              ...priceInputFormatter,
            ],
          ),
        ),
      ],
    );
  }
}
```

#### Sử dụng trong StopOrderEditDialog:
```dart
Widget _buildInputFieldsSection(BuildContext context) {
  return EditableStopOrderFields(
    volumeController: _volumeController,
    priceController: _priceController,
    activePriceController: _activePriceController,
    onVolumeChanged: _updateVolume,
    onPriceChanged: _updatePrice,
    onActivePriceChanged: _updateActivePrice,
  );
}

void _updateVolume(int newVolume) {
  setState(() {
    volume = newVolume;
    _volumeController.text = newVolume.toString();
  });
  // Validation logic nếu cần
}

void _updatePrice(double newPrice) {
  setState(() {
    price = newPrice;
    _priceController.text = newPrice.toString();
  });
  // Validation logic nếu cần
}

void _updateActivePrice(double newActivePrice) {
  setState(() {
    activePrice = newActivePrice;
    _activePriceController.text = newActivePrice.toString();
  });
  // Validation logic nếu cần
}
```

### Cách 3: Tạo abstract base class (Nâng cao)

**Ưu điểm:** Tái sử dụng logic validation
**Nhược điểm:** Phức tạp hơn

```dart
abstract class BaseStopOrderWidget extends StatefulWidget {
  final bool isEditMode;
  
  const BaseStopOrderWidget({
    super.key,
    this.isEditMode = false,
  });
}

abstract class BaseStopOrderState<T extends BaseStopOrderWidget> extends State<T> {
  late TextEditingController volumeController;
  late TextEditingController priceController;
  late TextEditingController activePriceController;
  
  // Common validation logic
  bool validateInputs() {
    // Shared validation logic
  }
  
  // Abstract methods cho subclass implement
  void onVolumeChanged(int volume);
  void onPriceChanged(double price);
  void onActivePriceChanged(double activePrice);
}
```

## Lợi ích của việc tái sử dụng

1. **Consistency**: UI/UX giống nhau giữa đặt lệnh và chỉnh sửa
2. **Validation**: Tái sử dụng logic validation từ `InputFieldBox`
3. **Animation**: Blink effect và visual feedback
4. **Input formatters**: Cùng logic format số liệu
5. **Maintainability**: Dễ bảo trì và cập nhật

## Khuyến nghị

**Chọn Cách 2** (Shared Widget) vì:
- Cân bằng giữa đơn giản và tái sử dụng
- Dễ test và maintain
- Có thể mở rộng cho các màn hình khác
- Tuân thủ DRY principle

## Files cần tạo/sửa đổi

1. **Tạo mới:**
   - `features/vp_trading/lib/screen/place_order/derivative/shared/editable_stop_order_fields.dart`

2. **Sửa đổi:**
   - `features/vp_trading/lib/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/stop_order/stop_order_edit_dialog.dart`

## Lưu ý khi implement

1. **Input Formatters**: Đảm bảo sử dụng đúng formatters cho từng loại input
2. **Validation**: Kiểm tra validation rules phù hợp với business logic
3. **Focus Management**: Quản lý focus giữa các field
4. **Error Handling**: Hiển thị lỗi validation phù hợp
5. **Animation**: Giữ nguyên blink animation cho consistency

---
*Tài liệu được tạo: [Ngày hiện tại]*
*Phiên bản: 1.0*