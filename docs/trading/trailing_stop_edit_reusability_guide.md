# Hướng dẫn tái sử dụng logic Trailing Stop cho màn chỉnh sửa

## Tổng quan
Tài liệu này mô tả cách tái sử dụng logic và UI components từ màn đặt lệnh trailing stop cho màn chỉnh sửa lệnh trailing stop với 3 trường: <PERSON><PERSON><PERSON><PERSON> đặt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> đ<PERSON>.

## Logic Đặt lệnh Trailing Stop hiện tại

### 1. UI Components chính:
- `TrailingStopWidget` - Widget chính cho trailing stop
- `StepPriceTextInputField` - Component input cho bước giá
- `RangeTextInputField` - Component input cho biên độ
- `DerivativeValidateConditionOrderCubit` - Quản lý validation và state

### 2. Cấu trúc logic hiện tại:
```dart
class TrailingStopWidget extends StatefulWidget {
  // Chỉ có 2 controllers: stepPrice và range
  final _stepPriceController = TextEditingController();
  final _rangeController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StepPriceTextInputField(stepPriceController: _stepPriceController),
        const SizedBox(height: 12),
        RangeTextInputField(rangeController: _rangeController),
      ],
    );
  }
}
```

### 3. Input Field Components:
- **StepPriceTextInputField**: Sử dụng `InputFieldBox` với validation qua `DerivativeValidateConditionOrderCubit`
- **RangeTextInputField**: Tương tự với validation riêng cho range
- **Validation**: `onChangeStepPrice()`, `onChangeRange()`, `stepPriceTap()`, `rangeTap()`

### 4. Validation & State Management:
- `DerivativeValidateConditionOrderCubit` xử lý validation
- Error states: `errorStepPrice`, `errorRange`
- Input formatters: `removeZeroStartInputFormatter`, `volumeInputFormatter`

## Các cách tái sử dụng cho màn chỉnh sửa

### Cách 1: Tái sử dụng trực tiếp existing components + thêm Volume field

**Ưu điểm:** Tận dụng tối đa code hiện có
**Nhược điểm:** Cần modify existing components

```dart
// Trong TrailingStopEditDialog
Widget _buildInputFieldsSection(BuildContext context) {
  return Column(
    children: [
      // Thêm Volume field mới
      _buildVolumeField(),
      const SizedBox(height: 16),
      
      // Tái sử dụng existing components
      StepPriceTextInputField(stepPriceController: _stepPriceController),
      const SizedBox(height: 12),
      RangeTextInputField(rangeController: _rangeController),
    ],
  );
}

Widget _buildVolumeField() {
  return Row(
    children: [
      Expanded(
        flex: 1,
        child: Text(
          'Khối lượng đặt',
          style: context.textStyle.body14?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
      ),
      Expanded(
        flex: 3,
        child: ColoredBox(
          color: vpColor.backgroundElevation0,
          child: InputFieldBox(
            controller: _volumeController,
            maxLength: 10,
            hintText: 'KL',
            onChange: (value) {
              final newVolume = int.tryParse(value) ?? 0;
              _updateVolume(newVolume);
            },
            focusNode: _volumeFocusNode,
            scrollPadding: 180,
            onTap: (increase) {
              final current = int.tryParse(_volumeController.text) ?? 0;
              _updateVolume(increase ? current + 1 : current - 1);
            },
            inputFormatters: [
              removeZeroStartInputFormatter,
              ...volumeInputFormatter,
            ],
          ),
        ),
      ),
    ],
  );
}
```

### Cách 2: Tạo shared widget hoàn chỉnh cho 3 trường (Khuyến nghị)

**Ưu điểm:** Component hoàn chỉnh, dễ tái sử dụng
**Nhược điểm:** Cần tạo component mới

#### Tạo file shared component:
**File:** `features/vp_trading/lib/screen/place_order/derivative/shared/editable_trailing_stop_fields.dart`

```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/screen/place_order/derivative/input_view/input_field_box.dart';
import 'package:vp_trading/screen/place_order/derivative/input_view/blink.dart';

class EditableTrailingStopFields extends StatefulWidget {
  final TextEditingController volumeController;
  final TextEditingController stepPriceController;
  final TextEditingController rangeController;
  final Function(int) onVolumeChanged;
  final Function(String) onStepPriceChanged;
  final Function(String) onRangeChanged;
  final bool enableValidation;

  const EditableTrailingStopFields({
    super.key,
    required this.volumeController,
    required this.stepPriceController,
    required this.rangeController,
    required this.onVolumeChanged,
    required this.onStepPriceChanged,
    required this.onRangeChanged,
    this.enableValidation = true,
  });

  @override
  State<EditableTrailingStopFields> createState() => _EditableTrailingStopFieldsState();
}

class _EditableTrailingStopFieldsState extends State<EditableTrailingStopFields> {
  late FocusNode _volumeFocusNode;
  late FocusNode _stepPriceFocusNode;
  late FocusNode _rangeFocusNode;
  
  bool _volumeBlink = false;
  bool _stepPriceBlink = false;
  bool _rangeBlink = false;

  @override
  void initState() {
    super.initState();
    _volumeFocusNode = FocusNode();
    _stepPriceFocusNode = FocusNode();
    _rangeFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _volumeFocusNode.dispose();
    _stepPriceFocusNode.dispose();
    _rangeFocusNode.dispose();
    super.dispose();
  }

  void _triggerBlink(String field) {
    setState(() {
      switch (field) {
        case 'volume':
          _volumeBlink = true;
          break;
        case 'stepPrice':
          _stepPriceBlink = true;
          break;
        case 'range':
          _rangeBlink = true;
          break;
      }
    });
    
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _volumeBlink = false;
          _stepPriceBlink = false;
          _rangeBlink = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.enableValidation) {
      return BlocBuilder<DerivativeValidateConditionOrderCubit, DerivativeValidateConditionOrderState>(
        builder: (context, state) => _buildFields(context, state),
      );
    }
    
    return _buildFields(context, null);
  }

  Widget _buildFields(BuildContext context, DerivativeValidateConditionOrderState? state) {
    return Column(
      children: [
        _buildVolumeField(state),
        const SizedBox(height: 16),
        _buildStepPriceField(state),
        const SizedBox(height: 16),
        _buildRangeField(state),
      ],
    );
  }

  Widget _buildVolumeField(DerivativeValidateConditionOrderState? state) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            'Khối lượng đặt',
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textPrimary,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: ColoredBox(
            color: vpColor.backgroundElevation0,
            child: Blink(
              blink: _volumeBlink,
              child: InputFieldBox(
                isError: false, // Volume thường không có validation phức tạp
                controller: widget.volumeController,
                maxLength: 10,
                hintText: 'KL',
                onChange: (value) {
                  final newVolume = int.tryParse(value) ?? 0;
                  widget.onVolumeChanged(newVolume);
                },
                focusNode: _volumeFocusNode,
                scrollPadding: 180,
                onTap: (increase) {
                  final current = int.tryParse(widget.volumeController.text) ?? 0;
                  final newVolume = increase ? current + 1 : current - 1;
                  if (newVolume >= 0) {
                    widget.onVolumeChanged(newVolume);
                    _triggerBlink('volume');
                  }
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  FilteringTextInputFormatter.digitsOnly,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStepPriceField(DerivativeValidateConditionOrderState? state) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Row(
            children: [
              Text(
                'Bước giá',
                style: context.textStyle.body14?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
              const SizedBox(width: 4),
              // Info icon có thể thêm nếu cần
            ],
          ),
        ),
        Expanded(
          flex: 3,
          child: ColoredBox(
            color: vpColor.backgroundElevation0,
            child: Blink(
              blink: _stepPriceBlink,
              child: InputFieldBox(
                isError: widget.enableValidation && 
                        state?.errorStepPrice.isError == true &&
                        widget.stepPriceController.text.isNotEmpty,
                controller: widget.stepPriceController,
                maxLength: 10,
                hintText: 'Chênh lệch',
                onChange: (value) {
                  widget.onStepPriceChanged(value);
                  if (widget.enableValidation) {
                    context.read<DerivativeValidateConditionOrderCubit>()
                        .onChangeStepPrice(value);
                  }
                },
                focusNode: _stepPriceFocusNode,
                scrollPadding: 180,
                onTap: (increase) {
                  if (widget.enableValidation) {
                    context.read<DerivativeValidateConditionOrderCubit>()
                        .stepPriceTap(
                          text: widget.stepPriceController.text,
                          increase: increase,
                        );
                  }
                  _triggerBlink('stepPrice');
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  ...volumeInputFormatter,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRangeField(DerivativeValidateConditionOrderState? state) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            'Biên độ',
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textPrimary,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: ColoredBox(
            color: vpColor.backgroundElevation0,
            child: Blink(
              blink: _rangeBlink,
              child: InputFieldBox(
                isError: widget.enableValidation && 
                        state?.errorRange.isError == true &&
                        widget.rangeController.text.isNotEmpty,
                controller: widget.rangeController,
                maxLength: 10,
                hintText: 'Chênh lệch',
                onChange: (value) {
                  widget.onRangeChanged(value);
                  if (widget.enableValidation) {
                    context.read<DerivativeValidateConditionOrderCubit>()
                        .onChangeRange(value);
                  }
                },
                focusNode: _rangeFocusNode,
                scrollPadding: 180,
                onTap: (increase) {
                  if (widget.enableValidation) {
                    context.read<DerivativeValidateConditionOrderCubit>()
                        .rangeTap(
                          text: widget.rangeController.text,
                          increase: increase,
                        );
                  }
                  _triggerBlink('range');
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  ...volumeInputFormatter,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
```

#### Sử dụng trong TrailingStopEditDialog:
```dart
class TrailingStopEditOrderDialog extends StatefulWidget {
  final ConditionOrderBookModel model;
  final Function(bool)? callBack;

  const TrailingStopEditOrderDialog({
    super.key,
    required this.model,
    this.callBack,
  });

  @override
  State<TrailingStopEditOrderDialog> createState() => _TrailingStopEditOrderDialogState();
}

class _TrailingStopEditOrderDialogState extends State<TrailingStopEditOrderDialog> {
  late TextEditingController _volumeController;
  late TextEditingController _stepPriceController;
  late TextEditingController _rangeController;
  
  int volume = 0;
  double stepPrice = 0.0;
  double range = 0.0;
  
  int _originalVolume = 0;
  double _originalStepPrice = 0.0;
  double _originalRange = 0.0;

  @override
  void initState() {
    super.initState();
    
    // Initialize values from model
    volume = widget.model.qty ?? 0;
    stepPrice = double.tryParse(widget.model.stepPrice ?? '0') ?? 0.0;
    range = double.tryParse(widget.model.range ?? '0') ?? 0.0;
    
    // Store original values
    _originalVolume = volume;
    _originalStepPrice = stepPrice;
    _originalRange = range;
    
    // Initialize controllers
    _volumeController = TextEditingController(text: volume.toString());
    _stepPriceController = TextEditingController(text: stepPrice.toString());
    _rangeController = TextEditingController(text: range.toString());
  }

  @override
  void dispose() {
    _volumeController.dispose();
    _stepPriceController.dispose();
    _rangeController.dispose();
    super.dispose();
  }

  void _updateVolume(int newVolume) {
    if (newVolume >= 0) {
      setState(() {
        volume = newVolume;
        _volumeController.text = newVolume.toString();
        _volumeController.selection = TextSelection.fromPosition(
          TextPosition(offset: _volumeController.text.length),
        );
      });
    }
  }

  void _updateStepPrice(String value) {
    final newStepPrice = double.tryParse(value) ?? 0.0;
    if (newStepPrice >= 0) {
      setState(() {
        stepPrice = newStepPrice;
        // Controller đã được update trong EditableTrailingStopFields
      });
    }
  }

  void _updateRange(String value) {
    final newRange = double.tryParse(value) ?? 0.0;
    if (newRange >= 0) {
      setState(() {
        range = newRange;
        // Controller đã được update trong EditableTrailingStopFields
      });
    }
  }

  bool _hasChanges() {
    return volume != _originalVolume ||
           stepPrice != _originalStepPrice ||
           range != _originalRange;
  }

  void _handleConfirm() {
    // Logic xử lý confirm với 3 giá trị mới
    final updatedModel = widget.model.copyWith(
      qty: volume,
      stepPrice: stepPrice.toString(),
      range: range.toString(),
    );
    
    // Call API update
    widget.callBack?.call(true);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Text(
              'Chỉnh sửa lệnh Trailing Stop',
              style: context.textStyle.heading16?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
            const SizedBox(height: 24),
            
            // Input fields
            EditableTrailingStopFields(
              volumeController: _volumeController,
              stepPriceController: _stepPriceController,
              rangeController: _rangeController,
              onVolumeChanged: _updateVolume,
              onStepPriceChanged: _updateStepPrice,
              onRangeChanged: _updateRange,
              enableValidation: false, // Có thể bật nếu cần validation
            ),
            
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: VPButton.secondary(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('Hủy'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: VPButton.primary(
                    onPressed: _hasChanges() ? _handleConfirm : null,
                    child: Text('Xác nhận'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

### Cách 3: Extend existing TrailingStopWidget

**Ưu điểm:** Tận dụng tối đa logic hiện có
**Nhược điểm:** Có thể làm phức tạp component gốc

```dart
class EditableTrailingStopWidget extends TrailingStopWidget {
  final TextEditingController volumeController;
  final Function(int) onVolumeChanged;
  final bool showVolumeField;

  const EditableTrailingStopWidget({
    super.key,
    required this.volumeController,
    required this.onVolumeChanged,
    this.showVolumeField = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (showVolumeField) ...[
          _buildVolumeField(context),
          const SizedBox(height: 12),
        ],
        super.build(context), // Gọi build của parent
      ],
    );
  }

  Widget _buildVolumeField(BuildContext context) {
    // Implementation tương tự như trên
  }
}
```

## So sánh các cách tiếp cận

| Tiêu chí | Cách 1 | Cách 2 | Cách 3 |
|----------|--------|--------|--------|
| **Tái sử dụng code** | Cao | Trung bình | Rất cao |
| **Độ phức tạp** | Thấp | Trung bình | Cao |
| **Maintainability** | Trung bình | Cao | Thấp |
| **Flexibility** | Thấp | Cao | Trung bình |
| **Testing** | Khó | Dễ | Khó |

## Khuyến nghị

**Chọn Cách 2** (Shared Widget) vì:
- Component độc lập, dễ test
- Có thể tái sử dụng cho các màn hình khác
- Validation có thể bật/tắt linh hoạt
- Tuân thủ Single Responsibility Principle
- Dễ maintain và extend

## Files cần tạo/sửa đổi

1. **Tạo mới:**
   - `features/vp_trading/lib/screen/place_order/derivative/shared/editable_trailing_stop_fields.dart`

2. **Sửa đổi:**
   - `features/vp_trading/lib/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/trailing_stop/trailing_stop_edit_dialog.dart`

3. **Import cần thiết:**
   ```dart
   import 'package:vp_trading/screen/place_order/derivative/shared/editable_trailing_stop_fields.dart';
   ```

## Lợi ích của việc tái sử dụng

1. **Consistency**: UI/UX giống nhau giữa đặt lệnh và chỉnh sửa
2. **Validation**: Tái sử dụng logic validation từ `DerivativeValidateConditionOrderCubit`
3. **Animation**: Blink effect và visual feedback
4. **Input formatters**: Cùng logic format số liệu
5. **Focus management**: Quản lý focus giữa các field
6. **Error handling**: Hiển thị lỗi validation nhất quán

## Lưu ý khi implement

1. **Input Formatters**: 
   - Volume: `FilteringTextInputFormatter.digitsOnly`
   - StepPrice/Range: `volumeInputFormatter` (cho phép decimal)

2. **Validation**: 
   - Có thể bật/tắt validation qua `enableValidation` parameter
   - Sử dụng existing validation logic từ `DerivativeValidateConditionOrderCubit`

3. **Focus Management**: 
   - Mỗi field có FocusNode riêng
   - Auto focus khi tap vào +/- buttons

4. **Animation**: 
   - Blink effect khi tap +/- buttons
   - Duration 200ms như existing components

5. **Error Handling**: 
   - Hiển thị error state từ validation cubit
   - Error chỉ hiện khi field không empty

6. **State Management**:
   - Local state cho UI updates
   - Callback functions để communicate với parent
   - Original values để detect changes

## Testing Strategy

1. **Unit Tests**:
   ```dart
   testWidgets('should update volume when + button tapped', (tester) async {
     // Test logic
   });
   
   testWidgets('should show error when validation fails', (tester) async {
     // Test validation
   });
   ```

2. **Integration Tests**:
   - Test toàn bộ flow edit trailing stop order
   - Test validation với real cubit

3. **Widget Tests**:
   - Test UI rendering
   - Test user interactions

---
*Tài liệu được tạo: [Ngày hiện tại]*
*Phiên bản: 1.0*
*Tác giả: VP Trading Team*