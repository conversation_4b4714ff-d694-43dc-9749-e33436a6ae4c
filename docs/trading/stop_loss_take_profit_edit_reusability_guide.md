# Hướng dẫn tái sử dụng logic Stop Loss/Take Profit cho màn chỉnh sửa

## Tổng quan
Tài liệu này mô tả cách tái sử dụng logic và UI components từ màn đặt lệnh stop loss/take profit cho màn chỉnh sửa lệnh điều kiện với các trường: <PERSON><PERSON><PERSON> lỗ, <PERSON><PERSON><PERSON> kích hoạt cắt lỗ.

## Logic Đặt lệnh Stop Loss/Take Profit hiện tại

### 1. UI Components chính:
- `StopLossTakeProfitWidget` - Widget chính cho stop loss/take profit
- `TakeProfitTextInputField` - Component input cho giá chốt lời
- `StopLossTextInputField` - Component input cho giá cắt lỗ
- `ConditionStopLossTextInputField` - Component input cho giá kích hoạt cắt lỗ
- `DerivativeValidateConditionOrderCubit` - Quản lý validation và state

### 2. Cấu trúc logic hiện tại:
```dart
class StopLossTakeProfitWidget extends StatefulWidget {
  // 3 controllers cho 3 trường
  final _stopLossController = TextEditingController();
  final _conditionStopLossController = TextEditingController();
  final _takeProfitController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TakeProfitTextInputField(takeProfitController: _takeProfitController),
        const SizedBox(height: 12),
        StopLossTextInputField(stopLossController: _stopLossController),
        const SizedBox(height: 12),
        ConditionStopLossTextInputField(
          conditionStopLossController: _conditionStopLossController,
        ),
      ],
    );
  }
}
```

### 3. Input Field Components:
- **TakeProfitTextInputField**: Sử dụng `InputFieldBox` với `onChangePrice(value)`
- **StopLossTextInputField**: Sử dụng `InputFieldBox` với `onChangePrice(value, stopLoss: true)`
- **ConditionStopLossTextInputField**: Sử dụng `InputFieldBox` với `onChangePrice(value, conditionStopLoss: true)`

### 4. Validation & State Management:
- `DerivativeValidateConditionOrderCubit` xử lý validation
- Error states: `errorTakeProfit`, `errorStopLoss`, `errorConditionStopLoss`
- Input formatters: `removeZeroStartInputFormatter`, `volumeInputFormatter`
- Price tap methods: `priceTap()` với các parameters khác nhau

## Các cách tái sử dụng cho màn chỉnh sửa

### Cách 1: Tái sử dụng trực tiếp existing components (chỉ cần 2 trường)

**Ưu điểm:** Tận dụng tối đa code hiện có
**Nhược điểm:** Cần modify để chỉ hiển thị 2 trường cần thiết

```dart
// Trong StopLossTakeEditDialog
Widget _buildInputFieldsSection(BuildContext context) {
  return Column(
    children: [
      // Chỉ sử dụng 2 components cần thiết
      StopLossTextInputField(stopLossController: _stopLossController),
      const SizedBox(height: 12),
      ConditionStopLossTextInputField(
        conditionStopLossController: _conditionStopLossController,
      ),
    ],
  );
}
```

### Cách 2: Tạo shared widget cho 2 trường cần thiết (Khuyến nghị)

**Ưu điểm:** Component tối ưu cho edit mode, dễ tái sử dụng
**Nhược điểm:** Cần tạo component mới

#### Tạo file shared component:
**File:** `features/vp_trading/lib/screen/place_order/derivative/shared/editable_stop_loss_fields.dart`

```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class EditableStopLossFields extends StatefulWidget {
  final TextEditingController stopLossController;
  final TextEditingController conditionStopLossController;
  final Function(String) onStopLossChanged;
  final Function(String) onConditionStopLossChanged;
  final bool enableValidation;
  final bool showLabels;

  const EditableStopLossFields({
    super.key,
    required this.stopLossController,
    required this.conditionStopLossController,
    required this.onStopLossChanged,
    required this.onConditionStopLossChanged,
    this.enableValidation = true,
    this.showLabels = true,
  });

  @override
  State<EditableStopLossFields> createState() => _EditableStopLossFieldsState();
}

class _EditableStopLossFieldsState extends State<EditableStopLossFields> {
  late FocusNode _stopLossFocusNode;
  late FocusNode _conditionStopLossFocusNode;
  
  bool _stopLossBlink = false;
  bool _conditionStopLossBlink = false;

  @override
  void initState() {
    super.initState();
    _stopLossFocusNode = FocusNode();
    _conditionStopLossFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _stopLossFocusNode.dispose();
    _conditionStopLossFocusNode.dispose();
    super.dispose();
  }

  void _triggerBlink(String field) {
    setState(() {
      switch (field) {
        case 'stopLoss':
          _stopLossBlink = true;
          break;
        case 'conditionStopLoss':
          _conditionStopLossBlink = true;
          break;
      }
    });
    
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _stopLossBlink = false;
          _conditionStopLossBlink = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.enableValidation) {
      return BlocBuilder<DerivativeValidateConditionOrderCubit, DerivativeValidateConditionOrderState>(
        builder: (context, state) => _buildFields(context, state),
      );
    }
    
    return _buildFields(context, null);
  }

  Widget _buildFields(BuildContext context, DerivativeValidateConditionOrderState? state) {
    return Column(
      children: [
        _buildStopLossField(state),
        const SizedBox(height: 16),
        _buildConditionStopLossField(state),
      ],
    );
  }

  Widget _buildStopLossField(DerivativeValidateConditionOrderState? state) {
    return Row(
      children: [
        if (widget.showLabels) ...[
          Expanded(
            flex: 1,
            child: Text(
              'Giá cắt lỗ',
              style: context.textStyle.body14?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
        ],
        Expanded(
          flex: widget.showLabels ? 3 : 1,
          child: ColoredBox(
            color: vpColor.backgroundElevation0,
            child: Blink(
              blink: _stopLossBlink,
              child: InputFieldBox(
                isError: widget.enableValidation && 
                        state?.errorStopLoss.isError == true &&
                        widget.stopLossController.text.isNotEmpty,
                controller: widget.stopLossController,
                maxLength: 10,
                hintText: 'Giá',
                onChange: (value) {
                  widget.onStopLossChanged(value);
                  if (widget.enableValidation) {
                    context.read<DerivativeValidateConditionOrderCubit>()
                        .onChangePrice(value, stopLoss: true);
                  }
                },
                focusNode: _stopLossFocusNode,
                scrollPadding: 180,
                onTap: (increase) {
                  if (widget.enableValidation) {
                    context.read<DerivativeValidateConditionOrderCubit>()
                        .priceTap(
                          text: widget.stopLossController.text,
                          increase: increase,
                          stopLoss: true,
                        );
                  }
                  _triggerBlink('stopLoss');
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  ...volumeInputFormatter,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConditionStopLossField(DerivativeValidateConditionOrderState? state) {
    return Row(
      children: [
        if (widget.showLabels) ...[
          Expanded(
            flex: 1,
            child: Text(
              'Giá kích hoạt cắt lỗ',
              style: context.textStyle.body14?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
        ],
        Expanded(
          flex: widget.showLabels ? 3 : 1,
          child: ColoredBox(
            color: vpColor.backgroundElevation0,
            child: Blink(
              blink: _conditionStopLossBlink,
              child: InputFieldBox(
                isError: widget.enableValidation && 
                        state?.errorConditionStopLoss.isError == true &&
                        widget.conditionStopLossController.text.isNotEmpty,
                controller: widget.conditionStopLossController,
                maxLength: 10,
                hintText: 'Giá',
                onChange: (value) {
                  widget.onConditionStopLossChanged(value);
                  if (widget.enableValidation) {
                    context.read<DerivativeValidateConditionOrderCubit>()
                        .onChangePrice(value, conditionStopLoss: true);
                  }
                },
                focusNode: _conditionStopLossFocusNode,
                scrollPadding: 180,
                onTap: (increase) {
                  if (widget.enableValidation) {
                    context.read<DerivativeValidateConditionOrderCubit>()
                        .priceTap(
                          text: widget.conditionStopLossController.text,
                          increase: increase,
                          conditionStopLoss: true,
                        );
                  }
                  _triggerBlink('conditionStopLoss');
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  ...volumeInputFormatter,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
```

#### Sử dụng trong StopLossTakeEditDialog:
```dart
class StopLossTakeEditDialog extends StatefulWidget {
  final ConditionOrderBookModel model;
  final Function(bool)? callBack;

  const StopLossTakeEditDialog({
    super.key,
    required this.model,
    this.callBack,
  });

  @override
  State<StopLossTakeEditDialog> createState() => _StopLossTakeEditDialogState();
}

class _StopLossTakeEditDialogState extends State<StopLossTakeEditDialog> {
  late TextEditingController _stopLossController;
  late TextEditingController _conditionStopLossController;
  
  double stopLossPrice = 0.0;
  double conditionStopLossPrice = 0.0;
  
  double _originalStopLossPrice = 0.0;
  double _originalConditionStopLossPrice = 0.0;

  @override
  void initState() {
    super.initState();
    
    // Initialize values from model
    stopLossPrice = double.tryParse(widget.model.priceSL ?? '0') ?? 0.0;
    conditionStopLossPrice = double.tryParse(widget.model.activepriceSL ?? '0') ?? 0.0;
    
    // Store original values
    _originalStopLossPrice = stopLossPrice;
    _originalConditionStopLossPrice = conditionStopLossPrice;
    
    // Initialize controllers
    _stopLossController = TextEditingController(
      text: stopLossPrice > 0 ? stopLossPrice.toStringAsFixed(1) : '',
    );
    _conditionStopLossController = TextEditingController(
      text: conditionStopLossPrice > 0 ? conditionStopLossPrice.toStringAsFixed(1) : '',
    );
  }

  @override
  void dispose() {
    _stopLossController.dispose();
    _conditionStopLossController.dispose();
    super.dispose();
  }

  void _updateStopLoss(String value) {
    final newPrice = double.tryParse(value) ?? 0.0;
    setState(() {
      stopLossPrice = newPrice;
    });
  }

  void _updateConditionStopLoss(String value) {
    final newPrice = double.tryParse(value) ?? 0.0;
    setState(() {
      conditionStopLossPrice = newPrice;
    });
  }

  bool _hasChanges() {
    return stopLossPrice != _originalStopLossPrice ||
           conditionStopLossPrice != _originalConditionStopLossPrice;
  }

  bool _isValidInput() {
    return stopLossPrice > 0 && conditionStopLossPrice > 0;
  }

  void _handleConfirm() {
    if (!_isValidInput()) {
      // Show error message
      return;
    }

    // Logic xử lý confirm với 2 giá trị mới
    final updatedModel = widget.model.copyWith(
      priceSL: stopLossPrice.toString(),
      activepriceSL: conditionStopLossPrice.toString(),
    );
    
    // Call API update - sử dụng endpoint từ derivative_path.dart
    // editTakeProfitOrStopLossOrder: "/external/v1/trade/accounts/{accountId}/condorders/BB/update"
    
    widget.callBack?.call(true);
    Navigator.of(context).pop();
  }

  void _handleCancel() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Chỉnh sửa lệnh Stop Loss/Take Profit',
              style: context.textStyle.headineBold6?.copyWith(
                color: vpColor.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Information section (read-only)
            _buildInformationSection(),
            const SizedBox(height: 16),

            // Input fields
            EditableStopLossFields(
              stopLossController: _stopLossController,
              conditionStopLossController: _conditionStopLossController,
              onStopLossChanged: _updateStopLoss,
              onConditionStopLossChanged: _updateConditionStopLoss,
              enableValidation: false, // Có thể bật nếu cần validation
              showLabels: true,
            ),
            
            const SizedBox(height: 24),
            
            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildInformationSection() {
    return Column(
      children: [
        _buildInfoRow('Loại lệnh', 'Stop Loss/Take Profit'),
        const SizedBox(height: 8),
        _buildInfoRow('Mã hợp đồng', widget.model.symbol ?? '-'),
        const SizedBox(height: 8),
        _buildInfoRow('Khối lượng', '${widget.model.qty ?? 0}'),
        if (widget.model.priceTP != null && widget.model.priceTP!.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildInfoRow('Giá chốt lời', widget.model.priceTP!),
        ],
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
            color: vpColor.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: VpsButton.secondaryXsSmall(
            title: 'Hủy',
            onPressed: _handleCancel,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: VpsButton.primaryXsSmall(
            title: 'Xác nhận',
            onPressed: _hasChanges() && _isValidInput() ? _handleConfirm : null,
          ),
        ),
      ],
    );
  }
}
```

### Cách 3: Tạo wrapper cho existing components

**Ưu điểm:** Tận dụng 100% logic hiện có
**Nhược điểm:** Có thể dư thừa code

```dart
class EditModeStopLossWrapper extends StatelessWidget {
  final ConditionOrderBookModel model;
  final Function(String, String) onChanged;

  const EditModeStopLossWrapper({
    super.key,
    required this.model,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => DerivativeValidateConditionOrderCubit(),
      child: StopLossTakeProfitWidget(
        initialStopLoss: model.priceSL,
        initialConditionStopLoss: model.activepriceSL,
        hideTakeProfit: true, // Ẩn take profit field
        onStopLossChanged: (value) => onChanged(value, ''),
        onConditionStopLossChanged: (value) => onChanged('', value),
      ),
    );
  }
}
```

## So sánh các cách tiếp cận

| Tiêu chí | Cách 1 | Cách 2 | Cách 3 |
|----------|--------|--------|--------|
| **Tái sử dụng code** | Cao | Trung bình | Rất cao |
| **Độ phức tạp** | Thấp | Trung bình | Thấp |
| **Maintainability** | Trung bình | Cao | Thấp |
| **Flexibility** | Thấp | Cao | Trung bình |
| **Performance** | Tốt | Tốt | Trung bình |
| **Code size** | Nhỏ | Trung bình | Nhỏ |

## Khuyến nghị

**Chọn Cách 2** (Shared Widget) vì:
- Component tối ưu cho edit mode
- Có thể tái sử dụng cho các màn hình edit khác
- Validation có thể bật/tắt linh hoạt
- Hiển thị label có thể tùy chỉnh
- Tuân thủ Single Responsibility Principle
- Dễ test và maintain

## API Integration

Sử dụng endpoint từ `derivative_path.dart`:
```dart
static const String editTakeProfitOrStopLossOrder =
    "/external/v1/trade/accounts/{accountId}/condorders/BB/update";
```

Request payload:
```dart
{
  "orderId": widget.model.orderId,
  "accountId": widget.model.accountId,
  "symbol": widget.model.symbol,
  "priceSL": stopLossPrice.toString(),
  "activepriceSL": conditionStopLossPrice.toString(),
  // Giữ nguyên các field khác
  "priceTP": widget.model.priceTP,
  "qty": widget.model.qty,
}
```

## Files cần tạo/sửa đổi

1. **Tạo mới:**
   - `features/vp_trading/lib/screen/place_order/derivative/shared/editable_stop_loss_fields.dart`

2. **Sửa đổi:**
   - `features/vp_trading/lib/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/stop_loss/stop_loss_take_edit_dialog.dart`

3. **Import cần thiết:**
   ```dart
   import 'package:vp_trading/screen/place_order/derivative/shared/editable_stop_loss_fields.dart';
   import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
   ```

## Lợi ích của việc tái sử dụng

1. **Consistency**: UI/UX giống nhau giữa đặt lệnh và chỉnh sửa
2. **Validation**: Tái sử dụng logic validation từ `DerivativeValidateConditionOrderCubit`
3. **Animation**: Blink effect và visual feedback
4. **Input formatters**: Cùng logic format số liệu
5. **Focus management**: Quản lý focus giữa các field
6. **Error handling**: Hiển thị lỗi validation nhất quán
7. **Price tap logic**: Tái sử dụng logic +/- buttons

## Lưu ý khi implement

1. **Input Formatters**: 
   - Sử dụng `volumeInputFormatter` cho price fields
   - `removeZeroStartInputFormatter` để tránh số 0 đầu

2. **Validation**: 
   - Có thể bật/tắt validation qua `enableValidation` parameter
   - Sử dụng existing validation logic với `stopLoss: true` và `conditionStopLoss: true`

3. **Focus Management**: 
   - Mỗi field có FocusNode riêng
   - Auto focus khi tap vào +/- buttons

4. **Animation**: 
   - Blink effect khi tap +/- buttons
   - Duration 200ms như existing components

5. **Error Handling**: 
   - Hiển thị error state từ validation cubit
   - Error chỉ hiện khi field không empty

6. **Price Logic**:
   - Stop Loss price: Giá cắt lỗ thực tế
   - Condition Stop Loss price: Giá kích hoạt cắt lỗ
   - Validation: Condition price thường >= Stop Loss price

7. **State Management**:
   - Local state cho UI updates
   - Callback functions để communicate với parent
   - Original values để detect changes
   - Validation cho input values

## Business Logic Notes

1. **Stop Loss vs Condition Stop Loss**:
   - **Stop Loss (priceSL)**: Giá thực tế sẽ bán khi điều kiện được kích hoạt
   - **Condition Stop Loss (activepriceSL)**: Giá kích hoạt điều kiện stop loss

2. **Validation Rules**:
   - Cả 2 giá trị phải > 0
   - Condition price thường >= Stop Loss price (tùy business rule)
   - Format theo quy định của sàn

3. **API Response Handling**:
   - Success: Refresh order book list
   - Error: Show error message, không đóng dialog

## Testing Strategy

1. **Unit Tests**:
   ```dart
   testWidgets('should update stop loss price when input changes', (tester) async {
     // Test input handling
   });
   
   testWidgets('should show error when validation fails', (tester) async {
     // Test validation
   });
   
   testWidgets('should detect changes correctly', (tester) async {
     // Test change detection
   });
   ```

2. **Integration Tests**:
   - Test toàn bộ flow edit stop loss/take profit order
   - Test API integration
   - Test validation với real cubit

3. **Widget Tests**:
   - Test UI rendering với different model states
   - Test user interactions (tap +/-, input text)
   - Test button states (enabled/disabled)

---
*Tài liệu được tạo: [Ngày hiện tại]*
*Phiên bản: 1.0*
*Tác giả: VP Trading Team*