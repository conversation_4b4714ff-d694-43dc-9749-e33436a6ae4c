import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class EditableStopOrderFields extends StatelessWidget {
  final TextEditingController volumeController;
  final TextEditingController priceController;
  final TextEditingController activePriceController;
  final Function(int) onVolumeChanged;
  final Function(double) onPriceChanged;
  final Function(double) onActivePriceChanged;
  final FocusNode? volumeFocusNode;
  final FocusNode? priceFocusNode;
  final FocusNode? activePriceFocusNode;

  const EditableStopOrderFields({
    super.key,
    required this.volumeController,
    required this.priceController,
    required this.activePriceController,
    required this.onVolumeChanged,
    required this.onPriceChanged,
    required this.onActivePriceChanged,
    this.volumeFocusNode,
    this.priceFocusNode,
    this.activePriceFocusNode,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildVolumeField(context),
        const SizedBox(height: 16),
        _buildPriceField(context),
        const SizedBox(height: 16),
        _buildActivePriceField(context),
      ],
    );
  }

  Widget _buildVolumeField(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Khối lượng đặt',
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        SizedBox(
          width: 137,
          child: InputFieldBox(
            controller: volumeController,
            hintText: 'KL',
            onChange: (value) {
              final newVolume = int.tryParse(value.replaceAll(',', '')) ?? 0;
              onVolumeChanged(newVolume);
            },
            focusNode: volumeFocusNode,
            onTap: (increase) {
              final current =
                  int.tryParse(volumeController.text.replaceAll(',', '')) ?? 0;
              onVolumeChanged(increase ? current + 1 : current - 1);
            },
            inputFormatters: [
              removeZeroStartInputFormatter,
              ...volumeInputFormatter,
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPriceField(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Giá đặt',
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        SizedBox(
          width: 137,
          child: InputFieldBox(
            controller: priceController,
            hintText: 'Giá',
            onChange: (value) {
              final newPrice =
                  double.tryParse(value.replaceAll(',', '')) ?? 0.0;
              onPriceChanged(newPrice);
            },
            focusNode: priceFocusNode,
            onTap: (increase) {
              final current =
                  double.tryParse(priceController.text.replaceAll(',', '')) ??
                  0.0;
              const step =
                  0.1; // Price step - could be dynamic based on stock info
              onPriceChanged(increase ? current + step : current - step);
            },
            inputFormatters: [
              removeZeroStartInputFormatter,
              LengthLimitingTextInputFormatter(8),
              ...priceInputFormatter,
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivePriceField(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Giá kích hoạt',
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Row(
          children: [
            // >= Button
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: vpColor.backgroundElevationMinus2,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  '≥',
                  style: context.textStyle.body14?.copyWith(
                    color: vpColor.iconSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 4),
            // Input field
            SizedBox(
              width: 137,
              child: InputFieldBox(
                controller: activePriceController,
                hintText: 'Giá KH',
                onChange: (value) {
                  final newActivePrice =
                      double.tryParse(value.replaceAll(',', '')) ?? 0.0;
                  onActivePriceChanged(newActivePrice);
                },
                focusNode: activePriceFocusNode,
                onTap: (increase) {
                  final current =
                      double.tryParse(
                        activePriceController.text.replaceAll(',', ''),
                      ) ??
                      0.0;
                  const step =
                      0.1; // Price step - could be dynamic based on stock info
                  onActivePriceChanged(
                    increase ? current + step : current - step,
                  );
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  LengthLimitingTextInputFormatter(8),
                  ...priceInputFormatter,
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
