import 'package:flutter/material.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/edit_order_conditional_widget.dart';

void showEditConditionalOrderDialog({
  required BuildContext context,
  void Function()? onConfirm,
  void Function(bool)? callBack,
  void Function()? onCancel,
  ConditionOrderBookModel? model,
}) {
  VPPopup.custom(
    padding: const EdgeInsets.all(20),
    child: EditOrderConditionalDialog(model: model),
  ).showDialog(context);
}
