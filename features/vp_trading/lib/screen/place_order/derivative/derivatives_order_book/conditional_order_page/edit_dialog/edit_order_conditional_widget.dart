import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/stop_loss/stop_loss_take_edit_dialog.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/stop_order/stop_order_edit_dialog.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/trailing_stop/trailing_stop_edit_dialog.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';

class EditOrderConditionalDialog extends StatelessWidget {
  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  const EditOrderConditionalDialog({super.key, this.callBack, this.model});

  @override
  Widget build(BuildContext context) {
    return _getWidgetByClassCd();
  }

  _getWidgetByClassCd() {
    switch (model?.conditionOrderTypeFuEnum) {
      case ConditionOrderTypeFuEnum.sto:
        return StopOrderEditDialog(model: model, callBack: callBack);
      case ConditionOrderTypeFuEnum.tso:
        return TrailingStopEditOrderDialog(model: model, callBack: callBack);
      case ConditionOrderTypeFuEnum.bb:
        return StopLossTakeEditDialog(model: model, callBack: callBack);
      case ConditionOrderTypeFuEnum.all:
        return const SizedBox.shrink();
      default:
        const VPBankLoading();
    }
  }
}
