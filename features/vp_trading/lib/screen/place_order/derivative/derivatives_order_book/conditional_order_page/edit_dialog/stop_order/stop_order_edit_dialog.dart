import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/stop_order/widgets/editable_stop_order_fields.dart';

class StopOrderEditDialog extends StatelessWidget {
  const StopOrderEditDialog({super.key, this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EditConditionOrderCubit(),
      child: _StopOrderEditDialogContent(callBack: callBack, model: model),
    );
  }
}

class _StopOrderEditDialogContent extends StatefulWidget {
  const _StopOrderEditDialogContent({this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  State<_StopOrderEditDialogContent> createState() =>
      _StopOrderEditDialogContentState();
}

class _StopOrderEditDialogContentState
    extends State<_StopOrderEditDialogContent> {
  late TextEditingController _volumeController;
  late TextEditingController _priceController;
  late TextEditingController _activePriceController;

  late FocusNode _volumeFocusNode;
  late FocusNode _priceFocusNode;
  late FocusNode _activePriceFocusNode;

  int volume = 0;
  double price = 0.0;
  double activePrice = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize values from model
    volume = widget.model?.qty?.toInt() ?? 0;
    price = double.tryParse(widget.model?.price ?? '0') ?? 0.0;
    activePrice = widget.model?.activePrice?.toDouble() ?? 0.0;

    // Initialize controllers
    _volumeController = TextEditingController(text: volume.toString());
    _priceController = TextEditingController(text: price.toStringAsFixed(1));
    _activePriceController = TextEditingController(
      text: activePrice.toStringAsFixed(1),
    );

    // Initialize focus nodes
    _volumeFocusNode = FocusNode();
    _priceFocusNode = FocusNode();
    _activePriceFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _volumeController.dispose();
    _priceController.dispose();
    _activePriceController.dispose();
    _volumeFocusNode.dispose();
    _priceFocusNode.dispose();
    _activePriceFocusNode.dispose();
    super.dispose();
  }

  void _updateVolume(int newVolume) {
    if (newVolume >= 0) {
      setState(() {
        volume = newVolume;
        _volumeController.text = newVolume.toString();
        _volumeController.selection = TextSelection.fromPosition(
          TextPosition(offset: _volumeController.text.length),
        );
      });
    }
  }

  void _updatePrice(double newPrice) {
    if (newPrice >= 0) {
      setState(() {
        price = newPrice;
        _priceController.text = newPrice.toStringAsFixed(1);
        _priceController.selection = TextSelection.fromPosition(
          TextPosition(offset: _priceController.text.length),
        );
      });
    }
  }

  void _updateActivePrice(double newActivePrice) {
    if (newActivePrice >= 0) {
      setState(() {
        activePrice = newActivePrice;
        _activePriceController.text = newActivePrice.toStringAsFixed(1);
        _activePriceController.selection = TextSelection.fromPosition(
          TextPosition(offset: _activePriceController.text.length),
        );
      });
    }
  }

  void _handleConfirm() {
    // Create the condition order request
    final request = ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: widget.model?.orderType ?? '',
      accountId: widget.model?.accountId ?? '',
      orderId: widget.model?.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: widget.model?.symbol ?? '',
        qty: volume,
        side: widget.model?.side?.toLowerCase() ?? '',
        type: "limit",
        price: price * 1000, // Convert to server format
        fromDate: widget.model?.fromDate ?? '',
        toDate: widget.model?.toDate ?? '',
        activePrice: activePrice * 1000, // Convert to server format
        activeType: widget.model?.activeType ?? '',
      ),
    );

    // Call the API
    context.read<EditConditionOrderCubit>().updateConditionOrder(
      request: request,
    );
  }

  void _handleCancel() {
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<EditConditionOrderCubit, EditConditionOrderState>(
      listener: (context, state) {
        if (state.status == EditConditionOrderStatus.success) {
          // Show success message
          showSnackBar(context, 'Sửa lệnh thành công', isSuccess: true);
          // Call parent callback and close dialog
          widget.callBack?.call(true);
          context.pop();
        } else if (state.status == EditConditionOrderStatus.failure) {
          // Show error message
          showSnackBar(
            context,
            state.errorMessage ?? 'Có lỗi xảy ra khi sửa lệnh',
            isSuccess: false,
          );
        }
      },
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Text(
              'Sửa lệnh',
              style: context.textStyle.headineBold6?.copyWith(
                color: context.colors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Information section
            _buildInformationSection(context),
            const SizedBox(height: 16),

            // Input fields section
            _buildInputFieldsSection(context),
            const SizedBox(height: 16),

            // Action buttons
            _buildActionButtons(context, state),
          ],
        );
      },
    );
  }

  Widget _buildInformationSection(BuildContext context) {
    return Column(
      children: [
        // Order Type (read-only)
        _buildInfoRow(context, label: 'Loại lệnh', value: 'Stop Order'),
        const SizedBox(height: 8),

        // Contract Code (read-only)
        _buildInfoRow(
          context,
          label: 'Mã hợp đồng',
          value: widget.model?.symbol ?? '-',
        ),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: context.colors.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
            color: valueColor ?? context.colors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInputFieldsSection(BuildContext context) {
    return EditableStopOrderFields(
      volumeController: _volumeController,
      priceController: _priceController,
      activePriceController: _activePriceController,
      onVolumeChanged: _updateVolume,
      onPriceChanged: _updatePrice,
      onActivePriceChanged: _updateActivePrice,
      volumeFocusNode: _volumeFocusNode,
      priceFocusNode: _priceFocusNode,
      activePriceFocusNode: _activePriceFocusNode,
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    EditConditionOrderState state,
  ) {
    final isLoading = state.status == EditConditionOrderStatus.loading;

    return Row(
      children: [
        Expanded(
          child: VpsButton.secondaryXsSmall(
            title: 'Đóng',
            onPressed: isLoading ? null : _handleCancel,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: VpsButton.primaryXsSmall(
            title: 'Xác nhận',
            onPressed: isLoading ? null : _handleConfirm,
          ),
        ),
      ],
    );
  }
}
