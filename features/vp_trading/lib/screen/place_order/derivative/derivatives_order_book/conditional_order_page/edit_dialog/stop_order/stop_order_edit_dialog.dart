import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';

class StopOrderEditDialog extends StatefulWidget {
  const StopOrderEditDialog({super.key, this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  State<StopOrderEditDialog> createState() => _StopOrderEditDialogState();
}

class _StopOrderEditDialogState extends State<StopOrderEditDialog> {
  late TextEditingController _volumeController;
  late TextEditingController _priceController;
  late TextEditingController _activePriceController;

  late FocusNode _volumeFocusNode;
  late FocusNode _priceFocusNode;
  late FocusNode _activePriceFocusNode;

  int volume = 0;
  double price = 0.0;
  double activePrice = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize values from model
    volume = widget.model?.qty?.toInt() ?? 0;
    price = double.tryParse(widget.model?.price ?? '0') ?? 0.0;
    activePrice = widget.model?.activePrice?.toDouble() ?? 0.0;

    // Initialize controllers
    _volumeController = TextEditingController(text: volume.toString());
    _priceController = TextEditingController(text: price.toStringAsFixed(1));
    _activePriceController = TextEditingController(
      text: activePrice.toStringAsFixed(1),
    );

    // Initialize focus nodes
    _volumeFocusNode = FocusNode();
    _priceFocusNode = FocusNode();
    _activePriceFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _volumeController.dispose();
    _priceController.dispose();
    _activePriceController.dispose();
    _volumeFocusNode.dispose();
    _priceFocusNode.dispose();
    _activePriceFocusNode.dispose();
    super.dispose();
  }

  void _updateVolume(int newVolume) {
    if (newVolume >= 0) {
      setState(() {
        volume = newVolume;
        _volumeController.text = newVolume.toString();
        _volumeController.selection = TextSelection.fromPosition(
          TextPosition(offset: _volumeController.text.length),
        );
      });
    }
  }

  void _updatePrice(double newPrice) {
    if (newPrice >= 0) {
      setState(() {
        price = newPrice;
        _priceController.text = newPrice.toStringAsFixed(1);
        _priceController.selection = TextSelection.fromPosition(
          TextPosition(offset: _priceController.text.length),
        );
      });
    }
  }

  void _updateActivePrice(double newActivePrice) {
    if (newActivePrice >= 0) {
      setState(() {
        activePrice = newActivePrice;
        _activePriceController.text = newActivePrice.toStringAsFixed(1);
        _activePriceController.selection = TextSelection.fromPosition(
          TextPosition(offset: _activePriceController.text.length),
        );
      });
    }
  }

  void _handleConfirm() {
    // TODO: Implement confirm logic
    widget.callBack?.call(true);
    context.pop();
  }

  void _handleCancel() {
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Title
        Text(
          'Sửa lệnh',
          style: context.textStyle.headineBold6?.copyWith(
            color: context.colors.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        // Information section
        _buildInformationSection(context),
        const SizedBox(height: 16),

        // Input fields section
        _buildInputFieldsSection(context),
        const SizedBox(height: 16),

        // Action buttons
        _buildActionButtons(context),
      ],
    );
  }

  Widget _buildInformationSection(BuildContext context) {
    return Column(
      children: [
        // Order Type (read-only)
        _buildInfoRow(context, label: 'Loại lệnh', value: 'Stop Order'),
        const SizedBox(height: 8),

        // Contract Code (read-only)
        _buildInfoRow(
          context,
          label: 'Mã hợp đồng',
          value: widget.model?.symbol ?? '-',
        ),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: context.colors.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
            color: valueColor ?? context.colors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInputFieldsSection(BuildContext context) {
    return Column(
      children: [
        // Volume input
        _buildInputField(
          context,
          label: 'Khối lượng đặt',
          controller: _volumeController,
          focusNode: _volumeFocusNode,
          onDecrease: () => _updateVolume(volume - 1),
          onIncrease: () => _updateVolume(volume + 1),
          onChanged: (value) {
            final newVolume = int.tryParse(value) ?? 0;
            _updateVolume(newVolume);
          },
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        ),
        const SizedBox(height: 16),

        // Price input
        _buildInputField(
          context,
          label: 'Giá đặt',
          controller: _priceController,
          focusNode: _priceFocusNode,
          onDecrease: () => _updatePrice(price - 0.1),
          onIncrease: () => _updatePrice(price + 0.1),
          onChanged: (value) {
            final newPrice = double.tryParse(value) ?? 0.0;
            _updatePrice(newPrice);
          },
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
          ],
        ),
        const SizedBox(height: 16),

        // Active price input with >= button
        _buildActivePriceField(context),
      ],
    );
  }

  Widget _buildInputField(
    BuildContext context, {
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    required VoidCallback onDecrease,
    required VoidCallback onIncrease,
    required ValueChanged<String> onChanged,
    required TextInputType keyboardType,
    required List<TextInputFormatter> inputFormatters,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: context.colors.textSecondary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        SizedBox(
          width: 137,
          child: VPTextField.small(
            controller: controller,
            focusNode: focusNode,
            textAlign: TextAlign.center,
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            onChanged: onChanged,
            style: context.textStyle.body14?.copyWith(
              color: context.colors.textPrimary,
            ),
            prefixIcon:
                (color) => IconButton(
                  padding: const EdgeInsets.all(0),
                  icon: Icon(
                    Icons.remove,
                    color: context.colors.iconPrimary,
                    size: 16,
                  ),
                  onPressed: onDecrease,
                ),
            suffixIcon:
                (color) => IconButton(
                  padding: const EdgeInsets.all(0),
                  icon: Icon(
                    Icons.add,
                    color: context.colors.iconPrimary,
                    size: 16,
                  ),
                  onPressed: onIncrease,
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivePriceField(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Giá kích hoạt',
            style: context.textStyle.body14?.copyWith(
              color: context.colors.textSecondary,
            ),
          ),
        ),
        Row(
          children: [
            // >= Button
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: context.colors.backgroundElevationMinus2,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '≥',
                style: context.textStyle.body14?.copyWith(
                  color: context.colors.iconSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 4),
            // Input field
            SizedBox(
              width: 137,
              child: VPTextField.small(
                controller: _activePriceController,
                focusNode: _activePriceFocusNode,
                textAlign: TextAlign.center,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                onChanged: (value) {
                  final newActivePrice = double.tryParse(value) ?? 0.0;
                  _updateActivePrice(newActivePrice);
                },
                style: context.textStyle.body14?.copyWith(
                  color: context.colors.textPrimary,
                ),
                prefixIcon:
                    (color) => IconButton(
                      padding: const EdgeInsets.all(0),
                      icon: Icon(
                        Icons.remove,
                        color: context.colors.iconPrimary,
                        size: 16,
                      ),
                      onPressed: () => _updateActivePrice(activePrice - 0.1),
                    ),
                suffixIcon:
                    (color) => IconButton(
                      padding: const EdgeInsets.all(0),
                      icon: Icon(
                        Icons.add,
                        color: context.colors.iconPrimary,
                        size: 16,
                      ),
                      onPressed: () => _updateActivePrice(activePrice + 0.1),
                    ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: VpsButton.secondaryXsSmall(
            title: 'Đóng',
            onPressed: _handleCancel,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: VpsButton.primaryXsSmall(
            title: 'Xác nhận',
            onPressed: _handleConfirm,
          ),
        ),
      ],
    );
  }
}
